import AOS from 'aos';
import 'aos/dist/aos.css';
import { useEffect } from 'react';
import gsap from 'gsap';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';
import { AboutImage, CVImage, CryptarithmsImage, MathBrainImage, WordSearchImage } from './assets';
import { FaWhatsapp, FaGithub, FaLinkedin, FaEnvelope } from "react-icons/fa";
import { FaReact, FaCss3Alt, FaHtml5, FaJs, } from 'react-icons/fa';
import { BiLogoTailwindCss } from "react-icons/bi";
import { SiCanva } from "react-icons/si";
import { VscVscode } from "react-icons/vsc";


gsap.registerPlugin(ScrollToPlugin);

function App() {
  const projects = [
    { title: 'Math Brain Game', image: MathBrainImage, link: 'https://gamemathbrain-main.vercel.app/' },
    { title: 'Cryptarithms', image: CryptarithmsImage, link: 'https://gamesandy.vercel.app/' },
    { title: 'Word Search', image: WordSearchImage, link: 'https://carikata.vercel.app/' }
  ];

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });
  }, []);

  useEffect(() => {
    // Scroll animation
    const links = document.querySelectorAll("nav a[href^='#']");
    links.forEach(link => {
      link.addEventListener('click', e => {
        e.preventDefault();
        const target = document.querySelector(link.getAttribute('href'));
        if (target) {
          gsap.to(window, {
            duration: 1,
            scrollTo: { y: target, offsetY: 50 },
            ease: "power2.out"
          });
        }
      });
    });

    return () => {
      links.forEach(link => {
        link.removeEventListener('click', () => { });
      });
    };
  }, []);

  return (
    <div className="min-h-screen bg-pink-50 font-sans text-gray-800">
      {/* Navbar */}
      <nav className="sticky top-0 z-50 flex justify-end space-x-6 p-4 transition-all duration-300 bg-pink-200 shadow-md" data-aos="fade-up" data-aos-delay={100}>
        <a href="#home" className="hover:underline transition">Home</a>
        <a href="#about" className="hover:underline transition">About</a>
        <a href="#projects" className="hover:underline transition">Projects</a>
        <a href="#contact" className="hover:underline transition">Contact</a>
      </nav>

      {/* Home */}
      <section id="home" className="h-screen flex flex-col md:flex-row items-center justify-center gap-8 px-4 text-left">
        <img src={AboutImage} alt="Profile" className="w-50 h-50 lg:w-72 lg:h-72 rounded-full object-cover" data-aos="zoom-in" data-aos-delay="100" />
        <div className="max-w-xl">
          <h1 className="text-3xl font-bold mb-2" data-aos="fade-down-left" data-aos-delay="100">Halo.. Saya Aris Tristianti</h1>
          <p className="text-sm text-gray-600" data-aos="fade-up-left" data-aos-delay="100">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit...
          </p>
          <a 
            href={CVImage} 
            download="CV_ArisTristianti.png"
            className="inline-block mt-4 px-6 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors"
            data-aos="fade-up-left" 
            data-aos-delay="200"
          >
            Download CV
          </a>
        </div>
      </section>

      {/* About */}
      <section id="about" className="py-20 px-6 bg-white" data-aos="fade-up" data-aos-delay="100">
        <div className="max-w-6xl mx-auto flex flex-col md:flex-row items-center gap-10">
          <div className="flex-1">
            <h2 className="text-3xl font-bold text-pink-900 mb-4" data-aos="fade-down" data-aos-delay="300">Tentang Saya</h2>
            <p className="text-gray-700 mb-6" data-aos="fade-up" data-aos-delay="400">
              Saya seorang front-end developer yang fokus pada pembuatan UI interaktif dan responsif.
              Saat ini aktif mengembangkan proyek dengan stack modern seperti React dan Tailwind CSS.
            </p>

            {/* Tools */}
            <div className="flex flex-wrap gap-4" data-aos="fade-up" data-aos-delay="500">
              <span className="flex items-center gap-2 bg-pink-100 text-pink-800 px-2 py-1 rounded-full text-sm shadow-sm">
                <FaReact className="text-blue-500 w-7 h-7" />
              </span>
              <span className="flex items-center gap-2 bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm shadow-sm">
                <BiLogoTailwindCss className="text-[#06B6D4] w-7 h-7" />
              </span>
              <span className="flex items-center gap-2 bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm shadow-sm">
                <FaJs className="text-yellow-500 w-7 h-7" />
              </span>
              <span className="flex items-center gap-2 bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm shadow-sm">
                <FaHtml5 className="text-orange-500 w-7 h-7" />
              </span>
              <span className="flex items-center gap-2 bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm shadow-sm">
                <FaCss3Alt className="text-blue-500 w-7 h-7" />
              </span>
              <span className="flex items-center gap-2 bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm shadow-sm">
                <SiCanva className="text-[#20C4CB] w-7 h-7" />
              </span>
              <span className="flex items-center gap-2 bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm shadow-sm">
                <VscVscode className="text-[#007ACC] w-7 h-7" />
              </span>
              <span className="flex items-center gap-2 bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm shadow-sm">
                <FaGithub className="text-gray-500 w-7 h-7" />
              </span>
            </div>
          </div>
          <div className="flex-1 flex justify-center" data-aos="zoom-in" data-aos-delay="200">
            <img src={AboutImage} alt="About" className="w-60 h-60 md:w-72 md:h-72 rounded-2xl object-cover shadow-lg" />
          </div>
        </div>
      </section>


      {/* Projects */}
      <section
        id="projects"
        className="relative py-20 px-6 bg-fixed bg-center bg-no-repeat bg-cover"
      >
        <div className="max-w-[90vw] mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-pink-900" data-aos="fade-down" data-aos-delay={100}>My Projects</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            {projects.map((project, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-md transition-all duration-500 ease-in-out transform hover:-translate-y-3 hover:scale-[1.02] hover:shadow-xl hover:bg-pink-50"
                data-aos="zoom-in"
                data-aos-delay={index * 100}
              >
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-52 object-cover rounded mb-6"
                />
                <h3 className="text-xl font-semibold text-center">{project.title}</h3>
                <a href={project.link} className="block text-center mt-4 w-full py-2 px-4 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors">
                  Lihat Game
                </a>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact */}
      <section className="bg-pink-100" id="contact" data-aos="fade-up" data-aos-delay={100}>
        <div className="container px-6 py-12 mx-auto">
          <div className="text-center">
            <p className="font-medium text-pink-900 text-3xl">Contact</p>
          </div>

          <div className="grid grid-cols-1 gap-12 mt-10 md:grid-cols-2 lg:grid-cols-4">
            <div className="flex flex-col items-center justify-center text-center" data-aos="fade-up" data-aos-delay="100">
              <span className="p-3 text-white rounded-full bg-pink-600">
                <FaEnvelope />
              </span>
              <h2 className="mt-4 text-lg font-medium text-gray-800">Email</h2>
              <a href="mailto:<EMAIL>" className="mt-2 text-pink-600 hover:underline"><EMAIL></a>
            </div>

            <div className="flex flex-col items-center justify-center text-center" data-aos="fade-up" data-aos-delay="200">
              <span className="p-3 text-white rounded-full bg-pink-600">
                <FaWhatsapp />
              </span>
              <h2 className="mt-4 text-lg font-medium text-gray-800">WhatsApp</h2>
              <a href="https://wa.me/+6281918178780" className="mt-2 text-pink-600 hover:underline">+62 819-1817-8780</a>
            </div>

            <div className="flex flex-col items-center justify-center text-center" data-aos="fade-up" data-aos-delay="300">
              <span className="p-3 text-white rounded-full bg-pink-600">
                <FaLinkedin />
              </span>
              <h2 className="mt-4 text-lg font-medium text-gray-800">Linkedin</h2>
              <a href="https://www.linkedin.com/in/aris-tristianti-95b776348" className="mt-2 text-pink-600 hover:underline">@aris-tristianti</a>
            </div>
            <div className="flex flex-col items-center justify-center text-center" data-aos="fade-up" data-aos-delay="400">
              <span className="p-3 text-white rounded-full bg-pink-600">
                <FaGithub />
              </span>
              <h2 className="mt-4 text-lg font-medium text-gray-800">Github</h2>
              <a href="https://github.com/aris-qwertyuiop" className="mt-2 text-pink-600 hover:underline">@aris-qwertyuiop</a>
            </div>
          </div>
        </div>
      </section>

    </div>
  );
}

export default App;